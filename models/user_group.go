// Package models 定义用户组管理相关的数据模型
package models

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UserGroup 用户组数据模型
type UserGroup struct {
	ID          primitive.ObjectID   `json:"id" bson:"_id"`
	Name        string               `json:"name" bson:"name"`                       // 用户组名称
	Code        string               `json:"code" bson:"code"`                       // 用户组唯一标识
	Description string               `json:"description" bson:"description"`         // 用户组描述
	UserIDs     []primitive.ObjectID `json:"userIds" bson:"userIds"`                 // 关联的用户ID列表
	CreateTime  string               `json:"createTime" bson:"createTime"`           // 创建时间
	UpdateTime  string               `json:"updateTime" bson:"updateTime,omitempty"` // 更新时间
}

// UserGroupCreateRequest 创建用户组请求结构体
type UserGroupCreateRequest struct {
	Name        string   `json:"name" binding:"required"`
	Code        string   `json:"code" binding:"required"`
	Description string   `json:"description,omitempty"`
	UserIDs     []string `json:"userIds,omitempty"`
}

// UserGroupUpdateRequest 更新用户组请求结构体
type UserGroupUpdateRequest struct {
	Name        string   `json:"name,omitempty"`
	Code        string   `json:"code,omitempty"`
	Description string   `json:"description,omitempty"`
	UserIDs     []string `json:"userIds,omitempty"` // 关联的用户ID列表
}

// UserGroupListRequest 用户组列表查询请求结构体
type UserGroupListRequest struct {
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"pageSize,default=10"`
	Name     string `form:"name,omitempty"`
	Code     string `form:"code,omitempty"`
}

// UserGroupListResponse 用户组列表响应结构体
type UserGroupListResponse struct {
	UserGroups []UserGroup `json:"userGroups"`
	Total      int64       `json:"total"`
}
