// Package models 定义用户管理相关的数据模型
package models

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UserAuthType 用户认证方式类型
type UserAuthType string

const (
	AuthTypeLocal UserAuthType = "local" // 本地认证
	AuthTypeLDAP  UserAuthType = "ldap"  // LDAP认证
)

// UserStatus 用户状态类型
type UserStatus string

const (
	UserStatusEnable  UserStatus = "enable"  // 启用状态
	UserStatusDisable UserStatus = "disable" // 禁用状态
)

// User 用户数据模型
type User struct {
	ID            primitive.ObjectID   `json:"id" bson:"_id,omitempty"`            // _id 字段保留 omitempty，因为创建时 MongoDB 会自动生成
	DisplayName   string               `json:"displayName" bson:"displayName"`     // 显示名称，必填
	Username      string               `json:"username" bson:"username"`           // 用户名，必填且唯一
	Email         string               `json:"email" bson:"email"`                 // 邮箱，可选但要存储到数据库
	Phone         string               `json:"phone" bson:"phone"`                 // 电话，可选但要存储到数据库
	AuthType      UserAuthType         `json:"authType" bson:"authType"`           // 认证类型，必填
	Status        UserStatus           `json:"status" bson:"status"`               // 用户状态，必填
	IDCard        string               `json:"idCard" bson:"idCard"`               // 身份证，可选但要存储到数据库
	Password      string               `json:"-" bson:"password"`                  // 密码，不返回给前端但要存储到数据库
	GroupIDs      []primitive.ObjectID `json:"groupIds" bson:"groupIds"`           // 关联的用户组ID列表，确保存储到数据库
	CreateTime    string               `json:"createTime" bson:"createTime"`       // 创建时间，必填
	LastLoginTime string               `json:"lastLoginTime" bson:"lastLoginTime"` // 最后登录时间，要存储到数据库
}

// UserCreateRequest 创建用户请求结构体
type UserCreateRequest struct {
	DisplayName     string       `json:"displayName" binding:"required"`
	Username        string       `json:"username" binding:"required"`
	Email           string       `json:"email,omitempty" binding:"omitempty,email"`
	Phone           string       `json:"phone,omitempty"`
	AuthType        UserAuthType `json:"authType" binding:"required,oneof=local ldap"`
	Status          UserStatus   `json:"status" binding:"required,oneof=enable disable"`
	IDCard          string       `json:"idCard,omitempty"`
	Password        string       `json:"password,omitempty"`
	ConfirmPassword string       `json:"confirmPassword,omitempty"` // 仅用于前端验证
	GroupIDs        []string     `json:"groupIds,omitempty"`        // 关联的用户组ID列表
}

// UserUpdateRequest 更新用户请求结构体
type UserUpdateRequest struct {
	DisplayName string       `json:"displayName,omitempty"`
	Username    string       `json:"username,omitempty"`
	Email       string       `json:"email,omitempty" binding:"omitempty,email"`
	Phone       string       `json:"phone,omitempty"`
	AuthType    UserAuthType `json:"authType,omitempty" binding:"omitempty,oneof=local ldap"`
	Status      UserStatus   `json:"status,omitempty" binding:"omitempty,oneof=enable disable"`
	IDCard      string       `json:"idCard,omitempty"`
	Password    string       `json:"password,omitempty"`
	GroupIDs    []string     `json:"groupIds,omitempty"` // 关联的用户组ID列表
}

// UserListRequest 用户列表查询请求结构体
type UserListRequest struct {
	Page        int          `form:"page,default=1"`
	PageSize    int          `form:"pageSize,default=10"`
	DisplayName string       `form:"displayName,omitempty"`
	Username    string       `form:"username,omitempty"`
	Phone       string       `form:"phone,omitempty"`
	AuthType    UserAuthType `form:"authType,omitempty" binding:"omitempty,oneof=local ldap"`
	Status      UserStatus   `form:"status,omitempty" binding:"omitempty,oneof=enable disable"`
	GroupID     string       `form:"groupId,omitempty"` // 用户组ID过滤
}

// UserListResponse 用户列表响应结构体
type UserListResponse struct {
	Users []User `json:"users"`
	Total int64  `json:"total"`
}
