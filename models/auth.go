// Package models 定义认证相关的数据模型
package models

import (
	"errors"
)

var (
	ErrUserNotExist      = errors.New("登录失败：用户不存在")
	ErrPasswordIncorrect = errors.New("登录失败：密码错误")
	ErrUserDisabled      = errors.New("登录失败：用户已被禁用")
	ErrInvalidAuthType   = errors.New("登录失败：未找到认证器")
)

// LoginRequest 登录请求结构体
type LoginRequest struct {
	Username string       `json:"username" binding:"required"`                  // 用户名，必填
	Password string       `json:"password" binding:"required"`                  // 密码，必填
	AuthType UserAuthType `json:"authType" binding:"required,oneof=local ldap"` // 认证类型，必填，默认为 local
}

// LoginResponse 登录响应结构体
type LoginResponse struct {
	Token string `json:"token"` // 登录令牌

}
