// Package crypto 提供密码加密和验证相关的工具函数
// 本包主要用于处理用户密码的哈希加密和验证操作
package crypto

import (
	"crypto/md5"
	"fmt"
)

// HashPassword 对明文密码进行哈希加密
// 使用 MD5 算法对输入的明文密码进行不可逆加密处理
// 参数:
//   - password: 需要加密的明文密码字符串
//
// 返回值:
//   - string: 经过 MD5 哈希后的密码字符串（十六进制格式）
//
// 注意: MD5 仅用于演示，生产环境建议使用更安全的算法如 bcrypt
func HashPassword(password string) string {
	h := md5.New()
	h.Write([]byte(password))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// VerifyPassword 验证明文密码与加密密码是否匹配
// 通过对明文密码进行相同的哈希算法处理，然后与已存储的哈希密码进行比较
// 参数:
//   - password: 待验证的明文密码字符串
//   - hashedPassword: 已经过哈希加密的密码字符串
//
// 返回值:
//   - bool: 如果密码匹配返回 true，否则返回 false
func VerifyPassword(password, hashedPassword string) bool {
	return HashPassword(password) == hashedPassword
}
