// Package jwt 提供 JWT token 生成和验证相关的工具函数
package jwt

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// JWTConfig JWT配置结构体
type JWTConfig struct {
	SecretKey       string `mapstructure:"secret_key"`       // JWT签名密钥
	ExpirationHours int    `mapstructure:"expiration_hours"` // 过期时间（小时）
	Issuer          string `mapstructure:"issuer"`           // 签发者
}

// JWT 配置默认值常量
const (
	// DefaultSecretKey 默认的 JWT 签名密钥
	DefaultSecretKey = "dunshanops-jwt-secret-key-2024"
	// DefaultExpirationHours 默认的 token 过期时间（小时）
	DefaultExpirationHours = 24
	// DefaultIssuer 默认的 token 签发者
	DefaultIssuer = "DunshanOps"
)

// Claims JWT 载荷结构体
type Claims struct {
	UserID primitive.ObjectID `json:"userId"` // 用户ID
	jwt.RegisteredClaims
}

// GenerateToken 生成 JWT token
// 参数:
//   - userID: 用户ID
//   - jwtConfig: JWT配置
//
// 返回值:
//   - string: 生成的 JWT token
//   - error: 错误信息
func GenerateToken(userID primitive.ObjectID, jwtConfig JWTConfig) (string, error) {
	// 使用默认值填充配置
	if jwtConfig.SecretKey == "" {
		jwtConfig.SecretKey = DefaultSecretKey
	}
	if jwtConfig.ExpirationHours == 0 {
		jwtConfig.ExpirationHours = DefaultExpirationHours
	}
	if jwtConfig.Issuer == "" {
		jwtConfig.Issuer = DefaultIssuer
	}

	// 设置过期时间
	now := time.Now()
	expirationTime := now.Add(time.Duration(jwtConfig.ExpirationHours) * time.Hour)

	// 创建 Claims
	claims := &Claims{
		UserID: userID,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    jwtConfig.Issuer,
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			NotBefore: jwt.NewNumericDate(now),
		},
	}

	// 创建 token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名并返回
	tokenString, err := token.SignedString([]byte(jwtConfig.SecretKey))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// ParseToken 解析 JWT token
// 参数:
//   - tokenString: JWT token 字符串
//   - secretKey: 签名密钥，如果为空则使用默认密钥
//
// 返回值:
//   - *Claims: 解析出的载荷信息
//   - error: 错误信息
func ParseToken(tokenString string, secretKey string) (*Claims, error) {
	// 使用默认值
	if secretKey == "" {
		secretKey = DefaultSecretKey
	}

	// 解析 token
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("无效的签名方法")
		}
		return []byte(secretKey), nil
	})

	if err != nil {
		return nil, err
	}

	// 验证 token 有效性
	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("无效的 token")
}

// ValidateToken 验证 JWT token 是否有效
// 参数:
//   - tokenString: JWT token 字符串
//   - secretKey: 签名密钥，如果为空则使用默认密钥
//
// 返回值:
//   - bool: token 是否有效
//   - error: 错误信息
func ValidateToken(tokenString string, secretKey string) (bool, error) {
	_, err := ParseToken(tokenString, secretKey)
	if err != nil {
		return false, err
	}
	return true, nil
}

// RefreshToken 刷新 JWT token
// 参数:
//   - tokenString: 原始 JWT token 字符串
//   - jwtConfig: JWT配置
//
// 返回值:
//   - string: 新的 JWT token
//   - error: 错误信息
func RefreshToken(tokenString string, jwtConfig JWTConfig) (string, error) {
	// 解析原始 token
	claims, err := ParseToken(tokenString, jwtConfig.SecretKey)
	if err != nil {
		return "", err
	}

	// 生成新的 token
	return GenerateToken(claims.UserID, jwtConfig)
}
