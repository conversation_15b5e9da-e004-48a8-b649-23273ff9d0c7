# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below if you use Go modules)
/vendor/

# Go workspace file
*.code-workspace

# IDE/editor directories and files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
*.log

# GoLand project files
*.iml

# Test binary, coverage, and result files
*.coverprofile
*.testresult
*.benchresult

# Static analysis tool files
staticcheck.conf

# Go race detector files
*.race

# Go build output
/build/
/bin/ 

# config
config.yaml
