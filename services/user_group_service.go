// Package services 用户组管理服务模块
// 提供用户组的增删改查操作以及用户组成员管理功能
package services

import (
	"context"
	"errors"
	"time"

	"DunshanOps/database"
	"DunshanOps/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	// UserGroupCollectionName 用户组数据库集合名称
	UserGroupCollectionName = "user_groups"
)

// UserGroupService 用户组服务
// 封装用户组相关的业务逻辑，包括CRUD操作和成员管理
type UserGroupService struct {
	collection         *mongo.Collection            // MongoDB集合实例
	associationService *UserGroupAssociationService // 关联服务
}

// NewUserGroupService 创建用户组服务实例
// 返回初始化完成的用户组服务
func NewUserGroupService() *UserGroupService {
	return &UserGroupService{
		collection:         database.GetCollection(UserGroupCollectionName),
		associationService: NewUserGroupAssociationService(),
	}
}

// CreateUserGroup 创建用户组
// 验证用户组名称和编码的唯一性，验证关联用户的有效性，然后创建新的用户组
func (s *UserGroupService) CreateUserGroup(req *models.UserGroupCreateRequest) (*models.UserGroup, error) {
	// 1. 验证用户组名称唯一性
	if exists, err := s.isGroupNameExists(req.Name); err != nil {
		return nil, err
	} else if exists {
		return nil, errors.New("用户组名称已存在")
	}

	// 2. 验证用户组编码唯一性
	if exists, err := s.isGroupCodeExists(req.Code); err != nil {
		return nil, err
	} else if exists {
		return nil, errors.New("用户组编码已存在")
	}

	// 3. 验证关联用户的有效性并转换为ObjectID
	var userIDs []primitive.ObjectID
	if len(req.UserIDs) > 0 {
		// 转换字符串ID为ObjectID
		userIDs = make([]primitive.ObjectID, len(req.UserIDs))
		for i, userID := range req.UserIDs {
			objectID, err := primitive.ObjectIDFromHex(userID)
			if err != nil {
				return nil, errors.New("无效的用户ID: " + userID)
			}
			userIDs[i] = objectID
		}
	} else {
		userIDs = []primitive.ObjectID{}
	}

	// 4. 构建用户组对象
	now := time.Now().Format(models.DateTimeLayout)

	userGroup := &models.UserGroup{
		ID:          primitive.NewObjectID(),
		Name:        req.Name,
		Code:        req.Code,
		Description: req.Description,
		UserIDs:     userIDs,
		CreateTime:  now,
		UpdateTime:  now,
	}

	// 5. 保存到数据库
	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	_, err := s.collection.InsertOne(ctx, userGroup)
	if err != nil {
		return nil, err
	}

	// 6. 同步用户组到用户中
	if len(userIDs) > 0 {
		if err := s.associationService.SyncGroupToUsers(userGroup.ID, userIDs); err != nil {
			// 如果同步失败，删除已创建的用户组
			s.collection.DeleteOne(ctx, bson.M{"_id": userGroup.ID})
			return nil, err
		}
	}

	return userGroup, nil
}

// GetUserGroupList 获取用户组列表
// 支持按名称和编码进行模糊搜索，支持分页查询，返回用户组列表和userIDs
func (s *UserGroupService) GetUserGroupList(req *models.UserGroupListRequest) (*models.UserGroupListResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	// 1. 构建查询条件
	filter := bson.M{}
	if req.Name != "" {
		filter["name"] = bson.M{"$regex": req.Name, "$options": "i"} // 不区分大小写的模糊搜索
	}
	if req.Code != "" {
		filter["code"] = bson.M{"$regex": req.Code, "$options": "i"} // 不区分大小写的模糊搜索
	}

	// 2. 获取总数量
	total, err := s.collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 3. 分页查询
	skip := int64((req.Page - 1) * req.PageSize)
	limit := int64(req.PageSize)

	opts := options.Find().
		SetSkip(skip).
		SetLimit(limit).
		SetSort(bson.M{"createTime": -1}) // 按创建时间倒序排列

	cursor, err := s.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	// 4. 解析查询结果
	userGroups := make([]models.UserGroup, 0)
	if err := cursor.All(ctx, &userGroups); err != nil {
		return nil, err
	}

	// 6. 构建用户组列表响应
	return &models.UserGroupListResponse{
		UserGroups: userGroups,
		Total:      total,
	}, nil
}

// GetUserGroupByID 根据ID获取用户组详情
// 返回指定ID的用户组信息
func (s *UserGroupService) GetUserGroupByID(id string) (*models.UserGroup, error) {
	// 1. 验证和转换ObjectID
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New("无效的用户组ID")
	}

	// 2. 查询用户组
	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	var userGroup models.UserGroup
	err = s.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&userGroup)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New("用户组不存在")
		}
		return nil, err
	}

	// 3. 返回用户组信息
	return &userGroup, nil
}

// UpdateUserGroup 更新用户组
func (s *UserGroupService) UpdateUserGroup(id string, req *models.UserGroupUpdateRequest) (*models.UserGroup, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New("无效的用户组ID")
	}

	// 检查用户组是否存在
	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	var existingGroup models.UserGroup
	err = s.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&existingGroup)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New("用户组不存在")
		}
		return nil, err
	}

	// 如果要更新名称，检查是否已存在
	if req.Name != "" && req.Name != existingGroup.Name {
		if exists, err := s.isGroupNameExists(req.Name); err != nil {
			return nil, err
		} else if exists {
			return nil, errors.New("用户组名称已存在")
		}
	}

	// 如果要更新代码，检查是否已存在
	if req.Code != "" && req.Code != existingGroup.Code {
		if exists, err := s.isGroupCodeExists(req.Code); err != nil {
			return nil, err
		} else if exists {
			return nil, errors.New("用户组代码已存在")
		}
	}

	// 如果要更新用户ID列表，验证用户ID的有效性并转换为ObjectID
	var userIDs []primitive.ObjectID
	if req.UserIDs != nil {
		// 转换字符串ID为ObjectID
		userIDs = make([]primitive.ObjectID, len(req.UserIDs))
		for i, userID := range req.UserIDs {
			objectID, err := primitive.ObjectIDFromHex(userID)
			if err != nil {
				return nil, errors.New("无效的用户ID: " + userID)
			}
			userIDs[i] = objectID
		}
	}

	// 更新用户组关联
	if req.UserIDs != nil {
		if err := s.associationService.UpdateGroupUserAssociation(objectID, existingGroup.UserIDs, userIDs); err != nil {
			return nil, err
		}
	}

	// 构建更新字段
	updateFields := bson.M{
		"updateTime": time.Now().Format(models.DateTimeLayout),
	}
	if req.Name != "" {
		updateFields["name"] = req.Name
	}
	if req.Code != "" {
		updateFields["code"] = req.Code
	}
	if req.Description != "" {
		updateFields["description"] = req.Description
	}
	if req.UserIDs != nil {
		updateFields["userIds"] = userIDs
	}

	// 执行更新
	update := bson.M{"$set": updateFields}
	_, err = s.collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return nil, err
	}

	// 返回更新后的用户组信息
	var updatedGroup models.UserGroup
	err = s.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&updatedGroup)
	if err != nil {
		return nil, err
	}

	return &updatedGroup, nil
}

// DeleteUserGroup 删除用户组
func (s *UserGroupService) DeleteUserGroup(id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New("无效的用户组ID")
	}

	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	// 1. 检查用户组是否存在
	var userGroup models.UserGroup
	err = s.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&userGroup)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return errors.New("用户组不存在")
		}
		return err
	}

	// 2. 检查用户组是否有关联用户
	if len(userGroup.UserIDs) > 0 {
		return errors.New("用户组中存在成员，不能删除")
	}

	// 3. 执行删除操作
	result, err := s.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return err
	}

	if result.DeletedCount == 0 {
		return errors.New("用户组不存在")
	}

	// 4. 从所有用户中移除该用户组
	if err := s.associationService.RemoveGroupFromAllUsers(objectID); err != nil {
		// 记录错误但不回滚，因为用户组已被删除
		// 这里可以考虑使用日志记录错误
	}

	return nil
}

// isFieldExists 检查字段值是否已存在的通用方法
func (s *UserGroupService) isFieldExists(field, value string) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	count, err := s.collection.CountDocuments(ctx, bson.M{field: value})
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// isGroupNameExists 检查用户组名称是否已存在
func (s *UserGroupService) isGroupNameExists(name string) (bool, error) {
	return s.isFieldExists("name", name)
}

// isGroupCodeExists 检查用户组编码是否已存在
func (s *UserGroupService) isGroupCodeExists(code string) (bool, error) {
	return s.isFieldExists("code", code)
}
