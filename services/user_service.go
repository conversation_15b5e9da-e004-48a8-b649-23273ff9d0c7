// Package services 用户管理服务模块
// 提供用户的增删改查操作以及用户成员管理功能
package services

import (
	"context"
	"errors"
	"time"

	"DunshanOps/database"
	"DunshanOps/models"
	"DunshanOps/pkg/crypto"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	// UserCollectionName 用户集合名称
	UserCollectionName = "users"
)

// UserService 用户服务结构体
type UserService struct {
	collection         *mongo.Collection
	associationService *UserGroupAssociationService
}

// NewUserService 创建新的用户服务实例
func NewUserService() *UserService {
	return &UserService{
		collection:         database.GetCollection(UserCollectionName),
		associationService: NewUserGroupAssociationService(),
	}
}

// CreateUser 创建新用户
func (s *UserService) CreateUser(req *models.UserCreateRequest) (*models.User, error) {
	// 检查用户名是否已存在
	if exists, err := s.isUsernameExists(req.Username); err != nil {
		return nil, err
	} else if exists {
		return nil, errors.New("用户名已存在")
	}

	// 转换用户组ID字符串为ObjectID
	var groupIDs []primitive.ObjectID
	if len(req.GroupIDs) > 0 {
		groupIDs = make([]primitive.ObjectID, 0, len(req.GroupIDs))
		for _, groupID := range req.GroupIDs {
			objectID, err := primitive.ObjectIDFromHex(groupID)
			if err != nil {
				return nil, errors.New("无效的用户组ID: " + groupID)
			}
			groupIDs = append(groupIDs, objectID)
		}
	} else {
		groupIDs = []primitive.ObjectID{}
	}

	// 创建用户对象，确保所有字段都被正确初始化
	user := &models.User{
		ID:            primitive.NewObjectID(),
		DisplayName:   req.DisplayName,                          // 必填字段
		Username:      req.Username,                             // 必填字段
		Email:         req.Email,                                // 可选字段，直接赋值（可能为空字符串）
		Phone:         req.Phone,                                // 可选字段，直接赋值（可能为空字符串）
		AuthType:      req.AuthType,                             // 必填字段
		Status:        req.Status,                               // 必填字段
		IDCard:        req.IDCard,                               // 可选字段，直接赋值（可能为空字符串）
		Password:      "",                                       // 初始化为空，后续根据认证类型处理
		GroupIDs:      groupIDs,                                 // 用户组ID列表，已处理过的切片
		CreateTime:    time.Now().Format(models.DateTimeLayout), // 创建时间
		LastLoginTime: "",                                       // 最后登录时间，初始化为空字符串
	}

	// 本地认证用户必须有密码
	if req.AuthType == models.AuthTypeLocal {
		if req.Password == "" {
			return nil, errors.New("本地认证用户必须设置密码")
		}
		user.Password = crypto.HashPassword(req.Password)
	}

	// 插入数据库
	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	_, err := s.collection.InsertOne(ctx, user)
	if err != nil {
		return nil, err
	}

	// 同步用户到用户组中
	if len(groupIDs) > 0 {
		if err := s.associationService.SyncUserToGroups(user.ID, groupIDs); err != nil {
			// 如果同步失败，删除已创建的用户
			s.collection.DeleteOne(ctx, bson.M{"_id": user.ID})
			return nil, err
		}
	}

	// 返回响应
	return user, nil
}

// GetUserList 获取用户列表
func (s *UserService) GetUserList(req *models.UserListRequest) (*models.UserListResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	// 构建查询条件
	filter := bson.M{}
	if req.DisplayName != "" {
		filter["displayName"] = bson.M{"$regex": req.DisplayName, "$options": "i"}
	}
	if req.Username != "" {
		filter["username"] = bson.M{"$regex": req.Username, "$options": "i"}
	}
	if req.Phone != "" {
		filter["phone"] = bson.M{"$regex": req.Phone, "$options": "i"}
	}
	if req.AuthType != "" {
		filter["authType"] = req.AuthType
	}
	if req.Status != "" {
		filter["status"] = req.Status
	}
	// 按用户组ID过滤
	if req.GroupID != "" {
		groupObjectID, err := primitive.ObjectIDFromHex(req.GroupID)
		if err != nil {
			return nil, errors.New("无效的用户组ID")
		}
		filter["groupIds"] = bson.M{"$in": []primitive.ObjectID{groupObjectID}}
	}

	// 计算总数
	total, err := s.collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 计算分页
	skip := int64((req.Page - 1) * req.PageSize)
	limit := int64(req.PageSize)

	// 查询选项
	opts := options.Find().
		SetSkip(skip).
		SetLimit(limit).
		SetSort(bson.M{"createTime": -1}) // 按创建时间降序

	// 执行查询
	cursor, err := s.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	// 解析结果
	users := make([]models.User, 0)
	if err := cursor.All(ctx, &users); err != nil {
		return nil, err
	}

	return &models.UserListResponse{
		Users: users,
		Total: total,
	}, nil
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(id string) (*models.User, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New("无效的用户ID")
	}

	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	var user models.User
	err = s.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}

	return &user, nil
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(id string, req *models.UserUpdateRequest) (*models.User, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New("无效的用户ID")
	}

	// 检查用户是否存在
	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	var existingUser models.User
	err = s.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&existingUser)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}

	// 如果要更新用户名，检查是否已存在
	if req.Username != "" && req.Username != existingUser.Username {
		if exists, err := s.isUsernameExists(req.Username); err != nil {
			return nil, err
		} else if exists {
			return nil, errors.New("用户名已存在")
		}
	}

	// 构建更新字段
	updateFields := bson.M{}

	// 对于可选字段，使用 reflect 或直接判断是否需要更新
	// DisplayName - 必填字段，不能为空
	if req.DisplayName != "" {
		updateFields["displayName"] = req.DisplayName
	}
	// Username - 必填字段，不能为空
	if req.Username != "" {
		updateFields["username"] = req.Username
	}
	// Email - 可选字段，允许为空
	updateFields["email"] = req.Email
	// Phone - 可选字段，允许为空
	updateFields["phone"] = req.Phone
	// AuthType - 枚举字段，如果有值则更新
	if req.AuthType != "" {
		updateFields["authType"] = req.AuthType
	}
	// Status - 枚举字段，如果有值则更新
	if req.Status != "" {
		updateFields["status"] = req.Status
	}
	// IDCard - 可选字段，允许为空
	updateFields["idCard"] = req.IDCard
	// Password - 如果提供了新密码则更新
	if req.Password != "" {
		updateFields["password"] = crypto.HashPassword(req.Password)
	}

	// 处理用户组ID更新
	var groupIDs []primitive.ObjectID
	if len(req.GroupIDs) > 0 {
		groupIDs = make([]primitive.ObjectID, 0, len(req.GroupIDs))
		for _, groupID := range req.GroupIDs {
			objectID, err := primitive.ObjectIDFromHex(groupID)
			if err != nil {
				return nil, errors.New("无效的用户组ID: " + groupID)
			}
			groupIDs = append(groupIDs, objectID)
		}
	} else {
		groupIDs = []primitive.ObjectID{}
	}
	updateFields["groupIds"] = groupIDs

	// 更新用户组关联
	if err := s.associationService.UpdateUserGroupAssociation(objectID, existingUser.GroupIDs, groupIDs); err != nil {
		return nil, err
	}

	// 执行更新
	update := bson.M{"$set": updateFields}
	_, err = s.collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return nil, err
	}

	// 返回更新后的用户信息
	return s.GetUserByID(id)
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New("无效的用户ID")
	}

	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	// 先获取用户信息以便清理关联
	var user models.User
	err = s.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return errors.New("用户不存在")
		}
		return err
	}

	// 删除用户
	result, err := s.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return err
	}

	if result.DeletedCount == 0 {
		return errors.New("用户不存在")
	}

	// 从所有用户组中移除该用户
	s.associationService.RemoveUserFromAllGroups(objectID)

	return nil
}

// ToggleUserStatus 切换用户状态（启用/禁用）
func (s *UserService) ToggleUserStatus(id string, status models.UserStatus) (*models.User, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New("无效的用户ID")
	}

	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	// 更新状态
	update := bson.M{"$set": bson.M{"status": status}}
	result, err := s.collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return nil, err
	}

	if result.MatchedCount == 0 {
		return nil, errors.New("用户不存在")
	}

	// 返回更新后的用户信息
	return s.GetUserByID(id)
}

// GetUserByUsername 根据用户名获取用户
func (s *UserService) GetUserByUsername(username string) (*models.User, error) {
	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	var user models.User
	err := s.collection.FindOne(ctx, bson.M{"username": username}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, models.ErrUserNotExist
		}
		return nil, err
	}

	return &user, nil
}

// UpdateLastLoginTime 更新用户最后登录时间
func (s *UserService) UpdateLastLoginTime(userID primitive.ObjectID) error {
	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	// 更新最后登录时间
	update := bson.M{
		"$set": bson.M{
			"lastLoginTime": time.Now().Format(models.DateTimeLayout),
		},
	}

	result, err := s.collection.UpdateOne(ctx, bson.M{"_id": userID}, update)
	if err != nil {
		return err
	}

	if result.MatchedCount == 0 {
		return errors.New("用户不存在")
	}

	return nil
}

// isUsernameExists 检查用户名是否已存在
func (s *UserService) isUsernameExists(username string) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	count, err := s.collection.CountDocuments(ctx, bson.M{"username": username})
	if err != nil {
		return false, err
	}

	return count > 0, nil
}
