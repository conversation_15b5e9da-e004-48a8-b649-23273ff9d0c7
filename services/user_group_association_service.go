// Package services 用户和用户组关联服务模块
// 提供用户和用户组之间双向关联的同步操作
package services

import (
	"context"

	"DunshanOps/database"
	"DunshanOps/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// UserGroupAssociationService 用户和用户组关联服务
// 负责维护用户和用户组之间的双向关联关系
type UserGroupAssociationService struct {
	userCollection      *mongo.Collection // 用户集合
	userGroupCollection *mongo.Collection // 用户组集合
}

// NewUserGroupAssociationService 创建用户组关联服务实例
func NewUserGroupAssociationService() *UserGroupAssociationService {
	return &UserGroupAssociationService{
		userCollection:      database.GetCollection(UserCollectionName),
		userGroupCollection: database.GetCollection(UserGroupCollectionName),
	}
}

// SyncUserToGroups 将用户添加到指定的用户组中
// 当创建或更新用户时调用，确保用户组中的用户列表包含该用户
func (s *UserGroupAssociationService) SyncUserToGroups(userID primitive.ObjectID, groupIDs []primitive.ObjectID) error {
	if len(groupIDs) == 0 {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	// 为每个用户组添加该用户ID（如果还没有的话）
	for _, groupID := range groupIDs {
		filter := bson.M{"_id": groupID}
		update := bson.M{
			"$addToSet": bson.M{"userIds": userID}, // 使用 $addToSet 避免重复
		}
		_, err := s.userGroupCollection.UpdateOne(ctx, filter, update)
		if err != nil {
			return err
		}
	}

	return nil
}

// RemoveUserFromGroups 从指定的用户组中移除用户
// 当删除用户或更新用户的用户组关联时调用
func (s *UserGroupAssociationService) RemoveUserFromGroups(userID primitive.ObjectID, groupIDs []primitive.ObjectID) error {
	if len(groupIDs) == 0 {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	// 从每个用户组中移除该用户ID
	for _, groupID := range groupIDs {
		filter := bson.M{"_id": groupID}
		update := bson.M{
			"$pull": bson.M{"userIds": userID}, // 使用 $pull 移除指定的用户ID
		}
		_, err := s.userGroupCollection.UpdateOne(ctx, filter, update)
		if err != nil {
			return err
		}
	}

	return nil
}

// RemoveUserFromAllGroups 从所有用户组中移除指定用户
// 当删除用户时调用，确保所有用户组中都不包含该用户
func (s *UserGroupAssociationService) RemoveUserFromAllGroups(userID primitive.ObjectID) error {
	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	// 从所有包含该用户的用户组中移除该用户
	filter := bson.M{"userIds": userID}
	update := bson.M{
		"$pull": bson.M{"userIds": userID},
	}

	_, err := s.userGroupCollection.UpdateMany(ctx, filter, update)
	return err
}

// SyncGroupToUsers 将用户组ID添加到指定用户的用户组列表中
// 当创建或更新用户组时调用，确保用户的用户组列表包含该用户组
func (s *UserGroupAssociationService) SyncGroupToUsers(groupID primitive.ObjectID, userIDs []primitive.ObjectID) error {
	if len(userIDs) == 0 {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	// 为每个用户添加该用户组ID（如果还没有的话）
	for _, userID := range userIDs {
		filter := bson.M{"_id": userID}
		update := bson.M{
			"$addToSet": bson.M{"groupIds": groupID}, // 使用 $addToSet 避免重复
		}
		_, err := s.userCollection.UpdateOne(ctx, filter, update)
		if err != nil {
			return err
		}
	}

	return nil
}

// RemoveGroupFromUsers 从指定用户的用户组列表中移除用户组
// 当删除用户组或更新用户组的用户关联时调用
func (s *UserGroupAssociationService) RemoveGroupFromUsers(groupID primitive.ObjectID, userIDs []primitive.ObjectID) error {
	if len(userIDs) == 0 {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	// 从每个用户的用户组列表中移除该用户组ID
	for _, userID := range userIDs {
		filter := bson.M{"_id": userID}
		update := bson.M{
			"$pull": bson.M{"groupIds": groupID}, // 使用 $pull 移除指定的用户组ID
		}
		_, err := s.userCollection.UpdateOne(ctx, filter, update)
		if err != nil {
			return err
		}
	}

	return nil
}

// RemoveGroupFromAllUsers 从所有用户的用户组列表中移除指定用户组
// 当删除用户组时调用，确保所有用户的用户组列表中都不包含该用户组
func (s *UserGroupAssociationService) RemoveGroupFromAllUsers(groupID primitive.ObjectID) error {
	ctx, cancel := context.WithTimeout(context.Background(), models.DatabaseTimeout)
	defer cancel()

	// 从所有包含该用户组的用户中移除该用户组
	filter := bson.M{"groupIds": groupID}
	update := bson.M{
		"$pull": bson.M{"groupIds": groupID},
	}

	_, err := s.userCollection.UpdateMany(ctx, filter, update)
	return err
}

// UpdateUserGroupAssociation 更新用户的用户组关联
// 处理用户组变更时的双向同步：从旧的用户组中移除用户，将用户添加到新的用户组中
func (s *UserGroupAssociationService) UpdateUserGroupAssociation(userID primitive.ObjectID, oldGroupIDs, newGroupIDs []primitive.ObjectID) error {
	// 计算需要移除的用户组（在旧列表中但不在新列表中）
	toRemove := make([]primitive.ObjectID, 0)
	for _, oldID := range oldGroupIDs {
		found := false
		for _, newID := range newGroupIDs {
			if oldID == newID {
				found = true
				break
			}
		}
		if !found {
			toRemove = append(toRemove, oldID)
		}
	}

	// 计算需要添加的用户组（在新列表中但不在旧列表中）
	toAdd := make([]primitive.ObjectID, 0)
	for _, newID := range newGroupIDs {
		found := false
		for _, oldID := range oldGroupIDs {
			if newID == oldID {
				found = true
				break
			}
		}
		if !found {
			toAdd = append(toAdd, newID)
		}
	}

	// 从旧的用户组中移除用户
	if len(toRemove) > 0 {
		if err := s.RemoveUserFromGroups(userID, toRemove); err != nil {
			return err
		}
	}

	// 将用户添加到新的用户组中
	if len(toAdd) > 0 {
		if err := s.SyncUserToGroups(userID, toAdd); err != nil {
			return err
		}
	}

	return nil
}

// UpdateGroupUserAssociation 更新用户组的用户关联
// 处理用户变更时的双向同步：从旧用户的用户组列表中移除该用户组，将用户组添加到新用户的列表中
func (s *UserGroupAssociationService) UpdateGroupUserAssociation(groupID primitive.ObjectID, oldUserIDs, newUserIDs []primitive.ObjectID) error {
	// 计算需要移除的用户（在旧列表中但不在新列表中）
	toRemove := make([]primitive.ObjectID, 0)
	for _, oldID := range oldUserIDs {
		found := false
		for _, newID := range newUserIDs {
			if oldID == newID {
				found = true
				break
			}
		}
		if !found {
			toRemove = append(toRemove, oldID)
		}
	}

	// 计算需要添加的用户（在新列表中但不在旧列表中）
	toAdd := make([]primitive.ObjectID, 0)
	for _, newID := range newUserIDs {
		found := false
		for _, oldID := range oldUserIDs {
			if newID == oldID {
				found = true
				break
			}
		}
		if !found {
			toAdd = append(toAdd, newID)
		}
	}

	// 从旧用户的用户组列表中移除该用户组
	if len(toRemove) > 0 {
		if err := s.RemoveGroupFromUsers(groupID, toRemove); err != nil {
			return err
		}
	}

	// 将用户组添加到新用户的列表中
	if len(toAdd) > 0 {
		if err := s.SyncGroupToUsers(groupID, toAdd); err != nil {
			return err
		}
	}

	return nil
}
