// Package services 认证服务模块，提供统一的认证接口和多种认证方式支持
package services

import (
	"DunshanOps/interfaces"
	"DunshanOps/models"
)

// AuthService 认证服务，集成认证管理器逻辑
type AuthService struct {
	providers []interfaces.AuthProvider // 认证提供者列表
}

// NewAuthService 创建新的认证服务实例
func NewAuthService() *AuthService {
	service := &AuthService{
		providers: make([]interfaces.AuthProvider, 0),
	}

	// 注册默认的本地认证提供者
	service.RegisterProvider(NewLocalAuthProvider())

	return service
}

// RegisterProvider 注册认证提供者
func (s *AuthService) RegisterProvider(provider interfaces.AuthProvider) {
	s.providers = append(s.providers, provider)
}

// Login 执行用户登录认证
// 根据指定的认证类型选择对应的认证提供者进行认证
func (s *AuthService) Login(username, password string, authType models.UserAuthType) (*models.LoginResponse, error) {
	// 查找指定类型的认证提供者
	var targetProvider interfaces.AuthProvider
	for _, provider := range s.providers {
		if provider.GetProviderName() == string(authType) && provider.IsEnabled() {
			targetProvider = provider
			break
		}
	}

	// 如果没有找到对应的认证提供者
	if targetProvider == nil {
		return nil, models.ErrInvalidAuthType
	}

	// 使用指定的认证提供者进行认证
	return targetProvider.Authenticate(username, password)
}

// GetEnabledProviders 获取所有启用的认证提供者信息
func (s *AuthService) GetEnabledProviders() []string {
	var providers []string

	for _, provider := range s.providers {
		if provider.IsEnabled() {
			providers = append(providers, provider.GetProviderName())
		}
	}

	return providers
}

// GetProviderByName 根据名称获取认证提供者
func (s *AuthService) GetProviderByName(name string) interfaces.AuthProvider {
	for _, provider := range s.providers {
		if provider.GetProviderName() == name {
			return provider
		}
	}
	return nil
}
