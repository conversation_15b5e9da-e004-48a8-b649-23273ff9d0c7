// Package services 本地认证提供者实现
package services

import (
	"DunshanOps/config"
	"DunshanOps/interfaces"
	"DunshanOps/models"
	"DunshanOps/pkg/crypto"
	"DunshanOps/pkg/jwt"
)

// LocalAuth 本地认证提供者
type LocalAuth struct {
	userService *UserService // 用户服务
	enabled     bool         // 是否启用本地认证
}

// 确保 LocalAuth 实现了 AuthProvider 接口
var _ interfaces.AuthProvider = (*LocalAuth)(nil)

// NewLocalAuthProvider 创建新的本地认证提供者实例
func NewLocalAuthProvider() *LocalAuth {
	return &LocalAuth{
		userService: NewUserService(),
		enabled:     true, // 默认启用本地认证
	}
}

// Authenticate 执行本地用户认证
func (p *LocalAuth) Authenticate(username, password string) (*models.LoginResponse, error) {
	// 从数据库查询用户信息
	user, err := p.userService.GetUserByUsername(username)
	if err != nil {
		return nil, err // GetUserByUsername 已经返回了 ErrUserNotExist
	}

	// 验证用户状态必须为启用
	if user.Status != models.UserStatusEnable {
		return nil, models.ErrUserDisabled
	}

	// 验证密码
	if !crypto.VerifyPassword(password, user.Password) {
		return nil, models.ErrPasswordIncorrect
	}

	// 生成 JWT token
	token, err := p.generateToken(user)
	if err != nil {
		return nil, err
	}

	// 更新用户最后登录时间
	if err := p.userService.UpdateLastLoginTime(user.ID); err != nil {
		// 记录错误但不影响登录流程
		// 这里可以添加日志记录
	}

	return &models.LoginResponse{
		Token: token,
	}, nil
}

// GetProviderName 获取认证提供者名称
func (p *LocalAuth) GetProviderName() string {
	return "local"
}

// IsEnabled 检查本地认证是否启用
func (p *LocalAuth) IsEnabled() bool {
	return p.enabled
}

// generateToken 生成用户登录令牌
func (p *LocalAuth) generateToken(user *models.User) (string, error) {
	// 使用配置文件中的JWT配置生成 token
	return jwt.GenerateToken(user.ID, config.AppConf.JWT)
}
