package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"github.com/natefinch/lumberjack"
	"github.com/rs/zerolog"
)

// LogConfig 日志配置结构体
type LogConfig struct {
	Level         string `mapstructure:"level"`           // 日志级别
	AppLogPath    string `mapstructure:"app_log_path"`    // 应用日志文件路径
	AccessLogPath string `mapstructure:"access_log_path"` // 访问日志文件路径
	MaxSize       int    `mapstructure:"max_size"`        // 单个日志文件最大大小(MB)
	MaxBackups    int    `mapstructure:"max_backups"`     // 最大备份文件数
	MaxAge        int    `mapstructure:"max_age"`         // 日志文件最大保留天数
	Compress      bool   `mapstructure:"compress"`        // 是否压缩备份文件
}

var (
	// APPLogger 应用日志实例
	APPLogger zerolog.Logger
	// AccessLogger 访问日志实例
	AccessLogger zerolog.Logger
)

// 默认日志配置
const (
	DefaultLogLevel      = "info"
	DefaultAPPLogPath    = "logs/app.log"
	DefaultAccessLogPath = "logs/access.log"
	DefaultLogMaxSize    = 100
	DefaultLogMaxBackups = 3
	DefaultLogMaxAge     = 28
	DefaultLogCompress   = true
)

// InitLogger 初始化日志系统
func InitLogger(config LogConfig, mode string) error {
	// 设置日志级别
	level, err := zerolog.ParseLevel(config.Level)
	if err != nil {
		level = zerolog.InfoLevel
	}

	// 设置全局日志级别
	zerolog.SetGlobalLevel(level)

	// 设置全局日志格式
	zerolog.TimeFieldFormat = "2006-01-02 15:04:05.000"

	// 初始化应用日志
	if err := initAppLogger(config, mode); err != nil {
		return fmt.Errorf("初始化应用日志失败: %w", err)
	}

	// 初始化访问日志
	if err := initAccessLogger(config); err != nil {
		return fmt.Errorf("初始化访问日志失败: %w", err)
	}

	return nil
}

// initAppLogger 初始化应用日志
func initAppLogger(config LogConfig, mode string) error {
	// 创建应用日志写入器
	var writers []io.Writer

	// 应用日志文件写入器
	appFileWriter, err := newRollingFile(config.AppLogPath, config)
	if err != nil {
		return err
	}
	writers = append(writers, appFileWriter)

	// debug 模式下同时输出至控制台
	if mode == "debug" {
		consoleWriter := zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: time.RFC3339,
		}
		writers = append(writers, consoleWriter)
	}

	// 创建多写入器
	multiWriter := zerolog.MultiLevelWriter(writers...)

	// 创建应用日志实例
	APPLogger = zerolog.New(multiWriter).
		With().
		Timestamp().
		Logger()

	return nil
}

// initAccessLogger 初始化访问日志
func initAccessLogger(config LogConfig) error {
	// 创建访问日志文件写入器
	accessFileWriter, err := newRollingFile(config.AccessLogPath, config)
	if err != nil {
		return err
	}

	// 创建访问日志实例（只写入文件，不输出到控制台）
	AccessLogger = zerolog.New(accessFileWriter).
		With().
		Timestamp().
		Logger()

	return nil
}

// newRollingFile 创建日志分割写入器
func newRollingFile(filePath string, config LogConfig) (io.Writer, error) {
	// 确保日志目录存在
	dir := filepath.Dir(filePath)
	// 如果日志目录不存在，则创建
	if dir != "" && dir != "." {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return nil, fmt.Errorf("创建日志目录失败: %w (file_path: %s)", err, filePath)
		}
	}

	return &lumberjack.Logger{
		Filename:   filePath,
		MaxSize:    config.MaxSize,
		MaxBackups: config.MaxBackups,
		MaxAge:     config.MaxAge,
		Compress:   config.Compress,
	}, nil
}
