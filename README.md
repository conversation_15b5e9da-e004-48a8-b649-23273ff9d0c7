# DunshanOps-server

盾山运维平台后端

---

## 项目简介

DunshanOps 是盾山运维平台的后端服务，负责为前端提供 API 支持、数据处理和业务逻辑，实现自动化运维、资源管理、监控告警等核心功能。

## 主要功能

- **认证系统**：支持本地登录认证

## 技术栈

- **框架**：Gin (Go Web 框架)
- **数据库**：MongoDB
- **日志**：Zerolog + Lumberjack (日志轮转)
- **配置管理**：Viper
- **语言**：Go 1.23.0

## 项目结构

```
DunshanOps-server/
├── config/                 # 配置管理模块
│   └── config.go           # 配置加载和管理逻辑
├── database/               # 数据库模块
│   └── mongo.go            # MongoDB 连接和管理
├── handlers/               # HTTP 处理器模块
│   └── auth_handler.go     # 认证相关的 HTTP 处理器
├── interfaces/             # 接口定义模块
│   └── auth.go             # 认证相关接口定义
├── logger/                 # 日志系统模块
│   └── logger.go           # 日志初始化和配置
├── middleware/             # 中间件模块
│   └── access_log.go       # 访问日志中间件
├── models/                 # 数据模型模块
│   ├── auth.go             # 认证相关数据模型
│   └── response.go         # 响应数据模型
├── routers/                # 路由管理模块
│   ├── auth.go             # 认证相关路由
│   ├── router.go           # 路由管理中心
│   └── system.go           # 系统相关路由
├── services/               # 业务服务模块
│   ├── auth_service.go     # 认证服务
│   └── local_auth_service.go # 本地认证提供者
├── utils/                  # 工具函数模块
│   └── password.go         # 密码相关工具函数
├── logs/                   # 日志文件目录
├── config.yaml             # 应用配置文件
├── config.example.yaml     # 配置文件示例
├── main.go                 # 应用入口文件
├── go.mod                  # Go 模块依赖文件
├── go.sum                  # Go 模块校验文件
└── README.md               # 项目说明文档
```

## 快速开始

### 1. 环境要求
- Go 1.23.0+
- MongoDB 5.0+

### 2. 安装依赖
```bash
go mod tidy
```

### 3. 配置数据库
复制配置文件模板：
```bash
cp config.example.yaml config.yaml
```

编辑 `config.yaml` 文件，配置 MongoDB 连接：
```yaml
database:
  # MongoDB 连接 URI (必须指定)
  uri: "mongodb://localhost:27017"
  
  # 如果 MongoDB 开启了认证，请设置以下配置
  auth: true
  username: "your_username"
  password: "your_password"
```

### 4. 启动服务
```bash
go run main.go
```

或编译后运行：
```bash
go build -o dunshanops
./dunshanops
```

### 5. 验证服务
访问健康检查端点：
```bash
curl http://localhost:8080/health
```