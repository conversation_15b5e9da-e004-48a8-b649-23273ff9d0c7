// Package handlers 用户管理相关的 HTTP 处理器
package handlers

import (
	"net/http"

	"DunshanOps/logger"
	"DunshanOps/models"
	"DunshanOps/pkg/response"
	"DunshanOps/services"

	"github.com/gin-gonic/gin"
)

// UserHandler 用户处理器结构体
type UserHandler struct {
	userService *services.UserService
}

// NewUserHandler 创建新的用户处理器实例
func NewUserHandler() *UserHandler {
	return &UserHandler{
		userService: services.NewUserService(),
	}
}

// CreateUser 创建用户
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req models.UserCreateRequest

	// 解析请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.APPLogger.Warn().
			Str("operation", "create_user").
			Err(err).
			Msg("创建用户请求参数解析失败")
		c.<PERSON><PERSON>(http.StatusOK, response.ErrorResponse(400, "创建用户失败：请求参数格式错误"))
		return
	}

	// 调用服务创建用户
	user, err := h.userService.CreateUser(&req)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "create_user").
			Str("username", req.Username).
			Err(err).
			Msg("创建用户失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, "创建用户失败："+err.Error()))
		return
	}

	// 记录创建成功日志
	logger.APPLogger.Info().
		Str("operation", "create_user").
		Str("username", req.Username).
		Str("userId", user.ID.Hex()).
		Msg("用户创建成功")

	// 返回创建成功响应
	c.JSON(http.StatusOK, response.SuccessResponse(user))
}

// GetUserList 获取用户列表
func (h *UserHandler) GetUserList(c *gin.Context) {
	var req models.UserListRequest

	// 解析查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.APPLogger.Warn().
			Str("operation", "get_user_list").
			Err(err).
			Msg("用户列表查询参数解析失败")
		c.JSON(http.StatusOK, response.ErrorResponse(400, "获取用户列表失败：查询参数格式错误"))
		return
	}

	// 调用服务获取用户列表
	userList, err := h.userService.GetUserList(&req)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "get_user_list").
			Err(err).
			Msg("获取用户列表失败")
		c.JSON(http.StatusOK, response.ErrorResponse(500, "获取用户列表失败"))
		return
	}

	// 记录查询日志
	logger.APPLogger.Info().
		Str("operation", "get_user_list").
		Int("page", req.Page).
		Int("pageSize", req.PageSize).
		Int64("total", userList.Total).
		Msg("用户列表查询成功")

	// 返回用户列表
	c.JSON(http.StatusOK, response.SuccessResponse(userList))
}

// GetUserByID 根据ID获取用户
func (h *UserHandler) GetUserByID(c *gin.Context) {
	userID := c.Query("userid")
	if userID == "" {
		c.JSON(http.StatusOK, response.ErrorResponse(400, "获取用户信息失败：用户ID不能为空"))
		return
	}

	// 调用服务获取用户
	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "get_user_by_id").
			Str("userId", userID).
			Err(err).
			Msg("获取用户信息失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, "获取用户信息失败："+err.Error()))
		return
	}

	// 记录查询成功日志
	logger.APPLogger.Info().
		Str("operation", "get_user_by_id").
		Str("userId", userID).
		Msg("用户信息查询成功")

	// 返回用户信息
	c.JSON(http.StatusOK, response.SuccessResponse(user))
}

// UpdateUser 更新用户
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userID := c.Query("userid")
	if userID == "" {
		c.JSON(http.StatusOK, response.ErrorResponse(400, "更新用户失败：用户ID不能为空"))
		return
	}

	var req models.UserUpdateRequest

	// 解析请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.APPLogger.Warn().
			Str("operation", "update_user").
			Err(err).
			Msg("更新用户请求参数解析失败")
		c.JSON(http.StatusOK, response.ErrorResponse(400, "更新用户失败：请求参数格式错误"))
		return
	}

	// 调用服务更新用户
	user, err := h.userService.UpdateUser(userID, &req)
	if err != nil {
		logger.APPLogger.Warn().
			Str("operation", "update_user").
			Str("userId", userID).
			Err(err).
			Msg("更新用户失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, "更新用户失败："+err.Error()))
		return
	}

	// 记录更新成功日志
	logger.APPLogger.Info().
		Str("operation", "update_user").
		Str("userId", userID).
		Msg("用户更新成功")

	// 返回更新后的用户信息
	c.JSON(http.StatusOK, response.SuccessResponse(user))
}

// DeleteUser 删除用户
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userID := c.Query("userid")
	if userID == "" {
		c.JSON(http.StatusOK, response.ErrorResponse(400, "删除用户失败：用户ID不能为空"))
		return
	}

	// 调用服务删除用户
	err := h.userService.DeleteUser(userID)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "delete_user").
			Str("userId", userID).
			Err(err).
			Msg("删除用户失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, "删除用户失败："+err.Error()))
		return
	}

	// 记录删除成功日志
	logger.APPLogger.Info().
		Str("operation", "delete_user").
		Str("userId", userID).
		Msg("用户删除成功")

	// 返回删除成功响应
	c.JSON(http.StatusOK, response.SuccessResponse(gin.H{
		"message": "用户删除成功",
	}))
}

// EnableUser 启用用户
func (h *UserHandler) EnableUser(c *gin.Context) {
	userID := c.Query("userid")
	if userID == "" {
		c.JSON(http.StatusOK, response.ErrorResponse(400, "启用用户失败：用户ID不能为空"))
		return
	}

	// 调用服务启用用户
	user, err := h.userService.ToggleUserStatus(userID, models.UserStatusEnable)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "enable_user").
			Str("userId", userID).
			Err(err).
			Msg("启用用户失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, "启用用户失败："+err.Error()))
		return
	}

	// 记录启用成功日志
	logger.APPLogger.Info().
		Str("operation", "enable_user").
		Str("userId", userID).
		Msg("用户启用成功")

	// 返回启用后的用户信息
	c.JSON(http.StatusOK, response.SuccessResponse(user))
}

// DisableUser 禁用用户
func (h *UserHandler) DisableUser(c *gin.Context) {
	userID := c.Query("userid")
	if userID == "" {
		c.JSON(http.StatusOK, response.ErrorResponse(400, "禁用用户失败：用户ID不能为空"))
		return
	}

	// 调用服务禁用用户
	user, err := h.userService.ToggleUserStatus(userID, models.UserStatusDisable)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "disable_user").
			Str("userId", userID).
			Err(err).
			Msg("禁用用户失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, "禁用用户失败："+err.Error()))
		return
	}

	// 记录禁用成功日志
	logger.APPLogger.Info().
		Str("operation", "disable_user").
		Str("userId", userID).
		Msg("用户禁用成功")

	// 返回禁用后的用户信息
	c.JSON(http.StatusOK, response.SuccessResponse(user))
}
