// Package handlers 用户组管理相关的 HTTP 处理器
package handlers

import (
	"net/http"

	"DunshanOps/logger"
	"DunshanOps/models"
	"DunshanOps/pkg/response"
	"DunshanOps/services"

	"github.com/gin-gonic/gin"
)

// UserGroupHandler 用户组处理器结构体
type UserGroupHandler struct {
	userGroupService *services.UserGroupService
}

// NewUserGroupHandler 创建新的用户组处理器实例
func NewUserGroupHandler() *UserGroupHandler {
	return &UserGroupHandler{
		userGroupService: services.NewUserGroupService(),
	}
}

// CreateUserGroup 创建用户组
func (h *UserGroupHandler) CreateUserGroup(c *gin.Context) {
	var req models.UserGroupCreateRequest

	// 解析请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.APPLogger.Warn().
			Str("operation", "create_user_group").
			Err(err).
			Msg("创建用户组请求参数解析失败")
		c.J<PERSON>(http.StatusOK, response.ErrorResponse(400, "创建用户组失败：请求参数格式错误"))
		return
	}

	// 调用服务创建用户组
	userGroup, err := h.userGroupService.CreateUserGroup(&req)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "create_user_group").
			Str("groupName", req.Name).
			Err(err).
			Msg("创建用户组失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, "创建用户组失败："+err.Error()))
		return
	}

	// 记录创建成功日志
	logger.APPLogger.Info().
		Str("operation", "create_user_group").
		Str("groupName", req.Name).
		Str("groupId", userGroup.ID.Hex()).
		Msg("用户组创建成功")

	// 返回创建成功响应
	c.JSON(http.StatusOK, response.SuccessResponse(userGroup))
}

// GetUserGroupList 获取用户组列表
func (h *UserGroupHandler) GetUserGroupList(c *gin.Context) {
	var req models.UserGroupListRequest

	// 解析查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.APPLogger.Warn().
			Str("operation", "get_user_group_list").
			Err(err).
			Msg("用户组列表查询参数解析失败")
		c.JSON(http.StatusOK, response.ErrorResponse(400, "获取用户组列表失败：查询参数格式错误"))
		return
	}

	// 调用服务获取用户组列表
	userGroupList, err := h.userGroupService.GetUserGroupList(&req)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "get_user_group_list").
			Err(err).
			Msg("获取用户组列表失败")
		c.JSON(http.StatusOK, response.ErrorResponse(500, "获取用户组列表失败"))
		return
	}

	// 记录查询日志
	logger.APPLogger.Info().
		Str("operation", "get_user_group_list").
		Int("page", req.Page).
		Int("pageSize", req.PageSize).
		Int64("total", userGroupList.Total).
		Msg("用户组列表查询成功")

	// 返回用户组列表
	c.JSON(http.StatusOK, response.SuccessResponse(userGroupList))
}

// GetUserGroupByID 根据ID获取用户组
func (h *UserGroupHandler) GetUserGroupByID(c *gin.Context) {
	groupID := c.Query("groupid")
	if groupID == "" {
		c.JSON(http.StatusOK, response.ErrorResponse(400, "获取用户组信息失败：用户组ID不能为空"))
		return
	}

	// 调用服务获取用户组
	userGroup, err := h.userGroupService.GetUserGroupByID(groupID)
	if err != nil {
		logger.APPLogger.Error().
			Str("operation", "get_user_group_by_id").
			Str("groupId", groupID).
			Err(err).
			Msg("获取用户组信息失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, "获取用户组信息失败："+err.Error()))
		return
	}

	// 记录查询成功日志
	logger.APPLogger.Info().
		Str("operation", "get_user_group_by_id").
		Str("groupId", groupID).
		Msg("用户组信息查询成功")

	// 返回用户组信息
	c.JSON(http.StatusOK, response.SuccessResponse(userGroup))
}

// UpdateUserGroup 更新用户组
func (h *UserGroupHandler) UpdateUserGroup(c *gin.Context) {
	groupID := c.Query("groupid")
	if groupID == "" {
		c.JSON(http.StatusOK, response.ErrorResponse(400, "更新用户组失败：用户组ID不能为空"))
		return
	}

	var req models.UserGroupUpdateRequest

	// 解析请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.APPLogger.Warn().
			Str("operation", "update_user_group").
			Err(err).
			Msg("更新用户组请求参数解析失败")
		c.JSON(http.StatusOK, response.ErrorResponse(400, "更新用户组失败：请求参数格式错误"))
		return
	}

	// 调用服务更新用户组
	userGroup, err := h.userGroupService.UpdateUserGroup(groupID, &req)
	if err != nil {
		logger.APPLogger.Warn().
			Str("operation", "update_user_group").
			Str("groupId", groupID).
			Err(err).
			Msg("更新用户组失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, "更新用户组失败："+err.Error()))
		return
	}

	// 记录更新成功日志
	logger.APPLogger.Info().
		Str("operation", "update_user_group").
		Str("groupId", groupID).
		Msg("用户组更新成功")

	// 返回更新后的用户组信息
	c.JSON(http.StatusOK, response.SuccessResponse(userGroup))
}

// DeleteUserGroup 删除用户组
func (h *UserGroupHandler) DeleteUserGroup(c *gin.Context) {
	groupID := c.Query("groupid")
	if groupID == "" {
		c.JSON(http.StatusOK, response.ErrorResponse(400, "删除用户组失败：用户组ID不能为空"))
		return
	}

	// 调用服务删除用户组
	err := h.userGroupService.DeleteUserGroup(groupID)
	if err != nil {
		logger.APPLogger.Warn().
			Str("operation", "delete_user_group").
			Str("groupId", groupID).
			Err(err).
			Msg("删除用户组失败")
		c.JSON(http.StatusOK, response.ErrorResponse(201, "删除用户组失败："+err.Error()))
		return
	}

	// 记录删除成功日志
	logger.APPLogger.Info().
		Str("operation", "delete_user_group").
		Str("groupId", groupID).
		Msg("用户组删除成功")

	// 返回删除成功响应
	c.JSON(http.StatusOK, response.SuccessResponse(gin.H{
		"message": "用户组删除成功",
	}))
}
