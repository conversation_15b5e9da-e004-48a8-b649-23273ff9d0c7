package main

import (
	"fmt"
	"os"

	"DunshanOps/config"
	"DunshanOps/database"
	"DunshanOps/logger"
	"DunshanOps/middleware"
	"DunshanOps/routers"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置文件
	if err := config.InitConfig("config.yaml"); err != nil {
		fmt.Fprintf(os.Stderr, "初始化配置失败: %s\n", err.Error())
		os.Exit(255)
	}

	// 初始化日志系统
	if err := logger.InitLogger(config.AppConf.Log, config.AppConf.Server.Mode); err != nil {
		fmt.Fprintf(os.Stderr, "初始化日志系统失败: %s\n", err.Error())
		os.Exit(255)
	}

	// 初始化数据库连接
	if err := database.InitMongoDB(config.AppConf.Database); err != nil {
		logger.APPLogger.Fatal().Err(err).Msg("初始化数据库连接失败")
		os.Exit(255)
	}

	defer database.CloseMongoDB()

	// 根据配置设置 gin 模式
	serverMode := config.GetServerMode()
	switch serverMode {
	case "debug":
		gin.SetMode(gin.DebugMode)
	case "release":
		gin.SetMode(gin.ReleaseMode)
	case "test":
		gin.SetMode(gin.TestMode)
	default:
		logger.APPLogger.Warn().Msg("未知的服务器模式: " + serverMode + "，使用 release 模式")
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建 gin 引擎
	r := gin.Default()

	// 添加中间件
	r.Use(gin.Recovery())
	r.Use(middleware.AccessLogMiddleware())

	// 设置路由
	routers.InitRoutes(r)

	// 记录服务器启动信息
	serverAddr := config.GetServerAddr()

	logger.APPLogger.Info().
		Str("address", serverAddr).
		Str("mode", serverMode).
		Msg("服务器启动中")

	// 启动服务器
	if err := r.Run(serverAddr); err != nil {
		logger.APPLogger.Fatal().Msg("启动服务器失败: " + err.Error())
	}
}
