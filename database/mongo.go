package database

import (
	"context"
	"fmt"
	"time"

	"DunshanOps/logger"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// MongoConfig MongoDB 数据库配置结构体
// 包含连接MongoDB所需的所有配置参数
type MongoConfig struct {
	URI      string `mapstructure:"uri"`      // MongoDB 连接 URI
	Timeout  int    `mapstructure:"timeout"`  // 连接超时时间(秒)
	Auth     bool   `mapstructure:"auth"`     // 是否开启认证
	Username string `mapstructure:"username"` // 用户名(认证时必填)
	Password string `mapstructure:"password"` // 密码(认证时必填)
}

const (
	// DefaultMongoTimeout MongoDB 连接默认超时时间(秒)
	DefaultMongoTimeout = 10
	// DefaultMongoAuth MongoDB 默认认证开关
	DefaultMongoAuth = false
	// FixedDatabaseName 固定的数据库名称
	FixedDatabaseName = "dunshanops"
)

// MongoDB 全局客户端实例
var (
	// MongoClient MongoDB 客户端实例，用于管理连接
	MongoClient *mongo.Client
	// MongoDB 数据库实例，用于操作具体数据库
	MongoDB *mongo.Database
)

// InitMongoDB 初始化 MongoDB 连接
// 参数 config: MongoDB 配置信息
// 返回 error: 初始化失败时返回错误信息
func InitMongoDB(config MongoConfig) error {
	// 验证必填配置：检查 URI 是否已指定
	if config.URI == "" {
		return fmt.Errorf("MongoDB URI 未指定，请在配置文件中设置 database.uri")
	}

	// 验证认证配置：如果开启认证，检查用户名密码是否已指定
	if config.Auth {
		if config.Username == "" || config.Password == "" {
			return fmt.Errorf("开启认证时必须指定用户名和密码，请在配置文件中设置 database.username 和 database.password")
		}
	}

	// 记录初始化开始日志
	logger.APPLogger.Info().
		Str("uri", config.URI).
		Str("database", FixedDatabaseName).
		Int("timeout", config.Timeout).
		Bool("auth", config.Auth).
		Msg("正在初始化 MongoDB 连接")

	// 创建客户端选项，设置连接 URI
	clientOptions := options.Client().ApplyURI(config.URI)

	// 条件设置：如果开启认证，配置认证信息
	if config.Auth {
		credential := options.Credential{
			Username: config.Username,
			Password: config.Password,
		}
		clientOptions.SetAuth(credential)
		// 记录认证配置日志（不记录密码）
		logger.APPLogger.Info().
			Str("username", config.Username).
			Msg("已配置 MongoDB 认证信息")
	}

	// 设置连接超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(config.Timeout)*time.Second)
	defer cancel()

	// 创建 MongoDB 客户端（惰性连接，不会立即建立网络连接）
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return fmt.Errorf("创建 MongoDB 客户端失败: %w", err)
	}

	// 执行 Ping 测试实际网络连接（这里才会真正连接到数据库）
	if err := client.Ping(ctx, nil); err != nil {
		// 连接失败时尝试关闭客户端
		if disconnectErr := client.Disconnect(ctx); disconnectErr != nil {
			logger.APPLogger.Error().Err(disconnectErr).Msg("关闭失败的 MongoDB 连接时出错")
		}
		return fmt.Errorf("MongoDB 连接测试失败: %w", err)
	}

	// 设置全局客户端和数据库实例
	MongoClient = client
	MongoDB = client.Database(FixedDatabaseName)

	// 记录连接成功日志
	logger.APPLogger.Info().
		Str("database", FixedDatabaseName).
		Msg("MongoDB 连接成功")

	return nil
}

// CloseMongoDB 优雅关闭 MongoDB 连接
// 返回 error: 关闭失败时返回错误信息
func CloseMongoDB() error {
	// 检查客户端是否已初始化
	if MongoClient == nil {
		logger.APPLogger.Warn().Msg("MongoDB 客户端未初始化，无需关闭")
		return nil
	}

	// 设置关闭超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 断开数据库连接
	if err := MongoClient.Disconnect(ctx); err != nil {
		logger.APPLogger.Error().Err(err).Msg("关闭 MongoDB 连接失败")
		return fmt.Errorf("关闭 MongoDB 连接失败: %w", err)
	}

	// 清空全局变量
	MongoClient = nil
	MongoDB = nil

	logger.APPLogger.Info().Msg("MongoDB 连接已关闭")
	return nil
}

// GetCollection 获取指定名称的集合
// 参数 name: 集合名称
// 返回 *mongo.Collection: 集合实例，如果数据库未初始化则返回 nil
func GetCollection(name string) *mongo.Collection {
	// 检查数据库是否已初始化
	if MongoDB == nil {
		logger.APPLogger.Error().
			Str("collection", name).
			Msg("MongoDB 未初始化，无法获取集合")
		return nil
	}

	// 返回指定集合
	return MongoDB.Collection(name)
}

// HealthCheck 执行数据库健康检查
// 返回 error: 健康检查失败时返回错误信息
func HealthCheck() error {
	// 检查客户端是否已初始化
	if MongoClient == nil {
		return fmt.Errorf("MongoDB 客户端未初始化")
	}

	// 设置健康检查超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 执行 Ping 测试
	if err := MongoClient.Ping(ctx, nil); err != nil {
		return fmt.Errorf("MongoDB 健康检查失败: %w", err)
	}

	return nil
}
