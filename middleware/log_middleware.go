package middleware

import (
	"DunshanOps/logger"
	"time"

	"github.com/gin-gonic/gin"
)

// AccessLogMiddleware 访问日志中间件
// 在每次HTTP请求处理完毕后记录以下信息：
// - 客户端IP (client_ip)
// - 请求路径 (path)
// - 请求方法 (method)
// - 响应状态码 (status)
// - 请求处理耗时 (latency)
// - User-Agent (user_agent)
// - 请求内容长度 (content_length)
func AccessLogMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()                      // 请求开始时间
		path := c.Request.URL.Path               // 请求路径
		method := c.Request.Method               // 请求方法
		clientIP := c.ClientIP()                 // 客户端IP
		userAgent := c.Request.UserAgent()       // User-Agent
		contentLength := c.Request.ContentLength // 请求内容长度(字节)

		// 处理请求
		c.Next()

		// 请求完成后获取状态和处理耗时
		status := c.Writer.Status()
		latency := time.Since(start)

		// 记录访问日志
		logger.AccessLogger.Info().
			Str("client_ip", clientIP).
			Str("path", path).
			Str("method", method).
			Int("status", status).
			Str("latency", latency.String()).
			Int64("request_content_length", contentLength).
			Int("response_size", c.Writer.Size()).
			Str("user_agent", userAgent).
			Send()
	}
}
