// Package routers 用户组管理路由配置
package routers

import (
	"DunshanOps/handlers"

	"github.com/gin-gonic/gin"
)

// SetupUserGroupRoutes 设置用户组管理相关路由
func SetupUserGroupRoutes(r *gin.RouterGroup) {
	// 创建用户组处理器实例
	userGroupHandler := handlers.NewUserGroupHandler()

	// 用户组管理路由组
	userGroupGroup := r.Group("/user-groups")
	{
		// 获取用户组列表 - GET /api/user-groups/list
		userGroupGroup.GET("/list", userGroupHandler.GetUserGroupList)

		// 创建用户组 - POST /api/user-groups/create
		userGroupGroup.POST("/create", userGroupHandler.CreateUserGroup)

		// 获取用户组信息 - GET /api/user-groups/info?groupid=123
		userGroupGroup.GET("/info", userGroupHandler.GetUserGroupByID)

		// 删除用户组 - DELETE /api/user-groups/delete?groupid=123
		userGroupGroup.DELETE("/delete", userGroupHandler.DeleteUserGroup)

		// 更新用户组 - PUT /api/user-groups/update?groupid=123
		userGroupGroup.PUT("/update", userGroupHandler.UpdateUserGroup)

	}
}
