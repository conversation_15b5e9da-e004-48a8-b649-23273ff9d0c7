// Package routers 用户管理路由配置
package routers

import (
	"DunshanOps/handlers"

	"github.com/gin-gonic/gin"
)

// SetupUserRoutes 设置用户管理相关路由
func SetupUserRoutes(r *gin.RouterGroup) {
	// 创建用户处理器实例
	userHandler := handlers.NewUserHandler()

	// 用户管理路由组
	userGroup := r.Group("/users")
	{
		// 获取用户列表 - GET /api/users/list
		userGroup.GET("/list", userHandler.GetUserList)

		// 创建用户 - POST /api/users/create
		userGroup.POST("/create", userHandler.CreateUser)

		// 获取用户信息 - GET /api/users/info
		userGroup.GET("/info", userHandler.GetUserByID)

		// 删除用户 - DELETE /api/users/delete
		userGroup.DELETE("/delete", userHandler.DeleteUser)

		// 更新用户 - PUT /api/users/update
		userGroup.PUT("/update", userHandler.UpdateUser)

		// 启用用户 - PATCH /api/users/enable
		userGroup.PATCH("/enable", userHandler.EnableUser)

		// 禁用用户 - PATCH /api/users/disable
		userGroup.PATCH("/disable", userHandler.DisableUser)
	}
}
